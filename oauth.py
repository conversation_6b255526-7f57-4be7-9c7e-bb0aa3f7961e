import os
import secrets
import requests
from urllib.parse import urlenco<PERSON>, parse_qs, urlparse
from requests_oauthlib import OAuth2Session
import logging

logger = logging.getLogger(__name__)

class LinuxDoOAuth:
    """Linux.do OAuth2 认证处理类"""
    
    def __init__(self):
        # Linux.do OAuth2 配置
        self.client_id = "GZfEhWWjowkChfAn5xtD6SFNfxR9YUt9"
        self.client_secret = "e5FiHlz6YwBdEJOw1VtbOYpAt7TTr4J8"
        self.redirect_uri = "https://sd.exacg.cc/linux"
        
        # Linux.do OAuth2 端点
        self.authorization_base_url = "https://connect.linux.do/oauth2/authorize"
        self.token_url = "https://connect.linux.do/oauth2/token"
        self.user_info_url = "https://connect.linux.do/api/user"
        
        # OAuth2 作用域
        self.scope = ["read"]
    
    def get_authorization_url(self, state=None):
        """
        获取授权URL
        
        Args:
            state: 状态参数，用于防止CSRF攻击
            
        Returns:
            tuple: (authorization_url, state)
        """
        if not state:
            state = secrets.token_urlsafe(32)
        
        oauth = OAuth2Session(
            client_id=self.client_id,
            redirect_uri=self.redirect_uri,
            scope=self.scope,
            state=state
        )
        
        authorization_url, state = oauth.authorization_url(
            self.authorization_base_url,
            state=state
        )
        
        logger.info(f"生成授权URL: {authorization_url}")
        return authorization_url, state
    
    def get_access_token(self, authorization_response_url, state):
        """
        通过授权码获取访问令牌
        
        Args:
            authorization_response_url: 回调URL（包含授权码）
            state: 状态参数
            
        Returns:
            dict: 包含访问令牌的字典，失败时返回None
        """
        try:
            oauth = OAuth2Session(
                client_id=self.client_id,
                redirect_uri=self.redirect_uri,
                state=state
            )
            
            token = oauth.fetch_token(
                self.token_url,
                authorization_response=authorization_response_url,
                client_secret=self.client_secret
            )
            
            logger.info("成功获取访问令牌")
            return token
            
        except Exception as e:
            logger.error(f"获取访问令牌失败: {e}")
            return None
    
    def get_user_info(self, access_token):
        """
        使用访问令牌获取用户信息
        
        Args:
            access_token: 访问令牌
            
        Returns:
            dict: 用户信息，失败时返回None
        """
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(self.user_info_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            user_info = response.json()
            logger.info(f"成功获取用户信息: {user_info.get('username', 'unknown')}")
            
            return user_info
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
        except Exception as e:
            logger.error(f"解析用户信息失败: {e}")
            return None
    
    def create_user_from_oauth(self, user_info):
        """
        从OAuth用户信息创建本地用户数据
        
        Args:
            user_info: Linux.do返回的用户信息
            
        Returns:
            dict: 本地用户数据格式
        """
        # 从Linux.do用户信息提取必要字段
        username = user_info.get('username', '')
        email = user_info.get('email', '')
        user_id = user_info.get('id', '')
        avatar_url = user_info.get('avatar_template', '')
        
        # 为OAuth用户生成特殊的用户名前缀，避免与普通用户冲突
        local_username = f"linux_do_{username}" if username else f"linux_do_user_{user_id}"
        
        # 创建本地用户数据结构
        local_user_data = {
            'username': local_username,
            'email': email,
            'oauth_provider': 'linux_do',
            'oauth_id': str(user_id),
            'oauth_username': username,
            'avatar_url': avatar_url,
            'original_user_info': user_info  # 保存原始用户信息以备后用
        }
        
        logger.info(f"创建OAuth用户数据: {local_username}")
        return local_user_data
    
    def validate_callback_request(self, request_args):
        """
        验证OAuth回调请求
        
        Args:
            request_args: Flask request.args
            
        Returns:
            tuple: (is_valid, error_message, code, state)
        """
        # 检查是否有错误参数
        if 'error' in request_args:
            error = request_args.get('error', 'unknown_error')
            error_description = request_args.get('error_description', '未知错误')
            logger.error(f"OAuth回调错误: {error} - {error_description}")
            return False, f"授权失败: {error_description}", None, None
        
        # 检查必要参数
        code = request_args.get('code')
        state = request_args.get('state')
        
        if not code:
            logger.error("OAuth回调缺少授权码")
            return False, "授权码缺失", None, None
        
        if not state:
            logger.error("OAuth回调缺少状态参数")
            return False, "状态参数缺失", None, None
        
        logger.info("OAuth回调请求验证通过")
        return True, None, code, state


def get_linux_do_oauth():
    """获取Linux.do OAuth实例的工厂函数"""
    return LinuxDoOAuth()
